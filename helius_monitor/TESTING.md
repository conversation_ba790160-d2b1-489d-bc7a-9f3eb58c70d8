# Jito Event Handling Pipeline Testing Guide

This document provides comprehensive testing approaches for the dual-WebSocket Solana monitoring system's Jito event handling pipeline.

## Overview

The testing framework validates the complete flow from Jito event reception to Helius subscription management:

1. **Jito Event Reception** → Parse JSON events into `JitoEvent` enum
2. **Command Channel Communication** → Send `SubscriptionCommand` through channels  
3. **Helius Subscription Management** → Generate JSON-RPC messages for Helius
4. **State Management** → Track subscriptions and prevent duplicates

## Test Categories

### 1. Unit Tests (`cargo test`)

Located in `src/main.rs` under the `#[cfg(test)]` module.

**Coverage:**
- ✅ `parse_jito_event()` function with valid/invalid inputs
- ✅ `SubscriptionManager` state management operations
- ✅ Channel communication between tasks
- ✅ JSON-RPC message generation (`create_logs_subscribe_message`, `create_logs_unsubscribe_message`)
- ✅ Duplicate subscription prevention logic

**Run unit tests:**
```bash
cargo test
```

### 2. Integration Tests (`tests/integration_tests.rs`)

**Coverage:**
- ✅ End-to-end pipeline simulation with mock WebSockets
- ✅ Concurrent subscription management under load
- ✅ Duplicate subscription prevention in realistic scenarios
- ✅ Subscription state consistency across multiple operations

**Run integration tests:**
```bash
cargo test --test integration_tests
```

### 3. Smoke Test (`cargo run --bin smoke_test`)

Interactive smoke test that simulates the complete Jito event handling pipeline.

**Test Scenarios:**

#### Test 1: New Token Event
```json
{"type": "new_token", "mint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"}
```
- ✅ Parses event correctly
- ✅ Sends `SubscriptionCommand::Subscribe`
- ✅ Generates `logsSubscribe` JSON-RPC message
- ✅ Updates subscription state

#### Test 2: Duplicate Subscription Prevention
```json
{"type": "new_token", "mint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"}
```
- ✅ Detects existing subscription
- ✅ Skips duplicate subscription request
- ✅ No additional Helius messages sent

#### Test 3: Dev Sold Event
```json
{"type": "dev_sold", "mint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"}
```
- ✅ Parses event correctly
- ✅ Sends `SubscriptionCommand::Unsubscribe`
- ✅ Generates `logsUnsubscribe` JSON-RPC message
- ✅ Removes subscription from state

#### Test 4: Invalid Event Handling
```json
{"type": "unknown_event", "mint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"}
```
- ✅ Rejects invalid event type
- ✅ No commands sent through channel
- ✅ No Helius messages generated

**Run smoke test:**
```bash
cargo run --bin smoke_test
```

## Manual Testing with Live WebSockets

### Prerequisites
1. Valid `HELIUS_API_KEY` in `.env` file
2. Jito ShredStream running at `ws://127.0.0.1:9005` (optional)

### Test Procedure

1. **Start the monitor:**
   ```bash
   cargo run --bin helius_monitor
   ```

2. **Simulate Jito events** by sending JSON messages to the Jito WebSocket endpoint:
   ```bash
   # New token event
   echo '{"type": "new_token", "mint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"}' | websocat ws://127.0.0.1:9005
   
   # Dev sold event  
   echo '{"type": "dev_sold", "mint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"}' | websocat ws://127.0.0.1:9005
   ```

3. **Verify expected behavior:**
   - New token events should trigger Helius subscriptions
   - Dev sold events should trigger Helius unsubscriptions
   - Duplicate subscriptions should be prevented
   - Invalid events should be ignored

## Expected Output Patterns

### Successful New Token Processing
```
🆕 Jito: New token detected - EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v
📤 Subscribed to logs for mint: EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v (request_id: 123)
✅ Subscription confirmed for mint: EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v with ID: 456
```

### Successful Dev Sold Processing
```
💰 Jito: Dev sold detected - EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v
📤 Unsubscribed from mint: EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v (subscription_id: 456)
```

### Duplicate Prevention
```
🆕 Jito: New token detected - EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v
⚠️  Already subscribed to mint: EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v
```

## Validation Checklist

Before deploying to production, verify:

- [ ] All unit tests pass (`cargo test`)
- [ ] All integration tests pass (`cargo test --test integration_tests`)
- [ ] Smoke test completes successfully (`cargo run --bin smoke_test`)
- [ ] Manual testing with live WebSockets works as expected
- [ ] Subscription state is properly maintained across events
- [ ] Duplicate subscriptions are prevented
- [ ] Invalid events are handled gracefully
- [ ] JSON-RPC messages have correct format and commitment level
- [ ] Channel communication works reliably under load

## Performance Considerations

The testing framework validates:
- **Debug Sampling**: 1-in-100 sampling prevents log flooding
- **Atomic Counters**: Thread-safe debug logging
- **Efficient State Management**: O(1) subscription lookups
- **Channel Buffering**: Unbounded channels prevent blocking

## Troubleshooting

### Common Issues

1. **Tests timeout**: Increase timeout values in integration tests
2. **Channel closed errors**: Ensure proper task cleanup in tests
3. **JSON parsing failures**: Verify event format matches expected structure
4. **WebSocket connection issues**: Check network connectivity and API keys

### Debug Mode

Enable detailed logging:
```bash
RUST_LOG=debug cargo run --bin smoke_test
```

This comprehensive testing approach ensures the Jito event handling pipeline is robust and ready for production deployment.
